#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  ³ MT103RTE ºAutor  ³ Sergio Shoji       º Data ³ 22/09/2016  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³ Ponto de entrada para inicializar o array __aRtCCNFE       º±±
±±º          ³ Publico.                                                   º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
User Function MT103NFE()
//// Declaração da variavel publica para inicializar o array de Rateios por Centros de Custos para ser usada na 
//// User Function CONTGAT1 disparada por Gatilho e os Pontos de Entrada MT110LOK e MT110GRV 
Public __aRtCCNFE

__aRtCCNFE := {}

//SE FOR DEVOLUCAO ALTERA A FUNCAO DO F4
If l103Class .OR. l103Visual 
	//Se a nota foi originada no Protheus
	If SF1->F1_TIPO == 'D' .AND. SF1->(FieldPos("F1_MOTRET")) > 0 .AND. TcCanOpen(RetSqlName("PFZ")) .AND. Empty(SF1->F1_XIDPSA)
		DbSelectArea("PFZ")
		PFZ->( DbSetOrder(2))
		PFZ->( DbSeek(xFilial("PFZ")+SF1->(F1_SERIE+F1_DOC)) )
		If !PFZ->( EOF() )
			SetKey(VK_F4, {|| U_TS026INF() })
		EndIf
		
		If l103Class
			MT103SetRet(SF1->F1_MOTRET, SF1->F1_HISTRET)
		EndIf
	//Se a nota foi originada no PSA
	ElseIf SF1->F1_TIPO == 'D' .AND. SF1->(FieldPos("F1_MOTRET")) > 0 .AND. !Empty(SF1->F1_XIDPSA)
		
		SetKey(VK_F4, {|| U_TSRVA519() })
	
		If l103Class
			MT103SetRet(SF1->F1_MOTRET, SF1->F1_HISTRET)
		EndIf		
	EndIf
EndIf

return