#Include "Protheus.ch"

/*/{Protheus.doc} MT103TPC
	Possibilita a alteração do conteúdo do parâmetro MV_TESPCNF, que permite informar os códigos dos TES que não exigem pedido de compra.
	<AUTHOR>
	@since 26/06/2024
	@return cRetTes, string, TES que não exigem pedido de compra
/*/
User Function MT103TPC()
	Local cRetTes	:= PARAMIXB[1]
	Local nX		:= 1
	Local nPosTes	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_TES"})
	Local nPosQuant	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_QUANT"})
	Local lMT103Ncc := GetMv("TI_VLDPSAZ",, .T.)
    Local nNfOri	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_NFORI"})

	//Deixa a quantidade zerada quando TES for para permitir sim no F4_QTDZERO e ele for devolução do PSA
	if !empty(aCols[1,nNfOri]) .AND. Posicione("SF4",1, FWxFilial("SF4") + aCols[nX,nPosTes], "F4_QTDZERO" ) == "1" .AND. !EMPTY(SF1->F1_XIDPSA) .and. lMT103Ncc
		for nX := 1 to LEN(aCols)
			aCols[nX,nPosQuant]  = 0 
		next
	endIf
Return cRetTes
