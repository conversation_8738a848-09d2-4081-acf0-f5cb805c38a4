#Include "Protheus.ch"

/*/{Protheus.doc} MT103SE2
	Ponto-de-Entrada Possibilita a adição de campos ao aCols de informação
	<AUTHOR>
	@since 26/06/2024
	@return aRet, Vetor, aHeader de titulos financeiros
/*/
User Function MT103SE2()
	Local nX		:= 1
	Local nPosTes	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_TES"})
	Local nPosQuant	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_QUANT"})
	Local lVisuali	:= PARAMIXB[2]
	Local aRet		:= {} //Vetor de retorno com campos que deverao ser incluidos ao aHeader de titulos financeiros
	Local lMT103Ncc := GetMv("TI_VLDPSAZ",, .T.)
	Local nNfOri	:= aScan(aHeader,{|x| AllTrim(x[2]) == "D1_NFORI"})

	//Deixa a quantidade zerada quando TES for para permitir sim no F4_QTDZERO e ele for devolução do PSA
	if !lVisuali .AND. !empty(aCols[1,nNfOri]) .AND. Posicione("SF4",1, FWxFilial("SF4") + aCols[nX,nPosTes], "F4_QTDZERO" ) == "1" .AND. !EMPTY(SF1->F1_XIDPSA) .and. lMT103Ncc
		for nX := 1 to LEN(aCols)
			aCols[nX,nPosQuant]  = 0 
		next
	endIf

Return aRet
