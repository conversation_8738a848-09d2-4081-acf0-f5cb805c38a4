#INCLUDE "PROTHEUS.CH"

//-------------------------------------------------------------------
/*/{Protheus.doc} MT100GE2
Complementa a Gravação dos Títulos Financeiros a Pagar

<AUTHOR>            
@since 19/08/2015
@version 1.0
/*/
//-------------------------------------------------------------------
User Function MT100GE2

	//Tratamento Json para API de integração com Mercado Eletronico
	If SuperGetMv("TI_INTME",,.F.)
		U_TCOMJ003()
	EndIf

	//Realiza a gravação complementar do contas a pagar de acordo com o item do documento fiscal com o maior valor
	GRVENTSE2()

	//Realiza a gravação de vencimento pelo csv
	If FwIsInCallStack('U_TCOMA011')
	   GravaD1Imp()	   	   
	EndIf

Return Nil

//-------------------------------------------------------------------
/*/{Protheus.doc} GRVENTSE2
Complementa a Gravação dos Títulos Financeiros a Pagar considerando o   
item do documento de entrada com maior valor

<AUTHOR> Gomes            
@since 19/08/2015
@version 1.0
/*/
//-------------------------------------------------------------------

//cNFiscal -> Private da Funcao MATA103 com o Numero da Nota Fiscal
//cSerie -> Private da Funcao MATA103 com a Serie da Nota Fiscal
//cA100For -> Private da Funcao MATA103 com o cod do Fornecedor
//cLoja -> Private da Funcao MATA103 com a loja do Fornecedor

Static Function GRVENTSE2

	Local nVMax		:= 0 				//Maior valor entre os itens da NF
	Local nRecMax	:= 0 				//No. de Registro do Item de Maior Valor
	Local aArea		:= GetArea()		//Area Atual
	Local aAreaSD1	:= SD1->(GetArea())	//Area Atual do SD1 (Itens de NF de Entrada)
	Local nRecE2	:= SE2->(Recno())
	Local aAreaSC7	:= SC7->(GetArea())
	Local cDescProd	:= ''
	Local aAreaSDE	:= SDE->(GetArea())
	Local aPedidos  := {}               //Array para armazenar pedidos distintos
	Local cPedidos  := ""               //String com pedidos concatenados
	Local nI        := 0

	//Posiciona no documento Fiscal de acordo com as váriaveis privates
	SD1->(DbSetOrder(1))
	
	If SD1->(DbSeek(xFilial("SD1")+cNFiscal+cSerie+cA100For+cLoja))
		
		nVMax		:= SD1->D1_TOTAL
		nRecMax	:= SD1->(Recno())
		
		//Consider o item do documento de entrada com o maior valor 
		While	!SD1->(EOF()) .And. SD1->D1_FILIAL == xFilial("SD1") ;
			.And. SD1->D1_DOC == cNFiscal .And. SD1->D1_SERIE == cSerie ;
			.And. SD1->D1_FORNECE == cA100For .And. SD1->D1_LOJA == cLoja

			//Adiciona pedido ao array se ainda não existir
            If !Empty(SD1->D1_PEDIDO) .And. AScan(aPedidos, {|x| x == SD1->D1_PEDIDO}) == 0
                AAdd(aPedidos, SD1->D1_PEDIDO)
            EndIf

			If SD1->D1_TOTAL > nVMax
			
				nVMax	:= SD1->D1_TOTAL
				nRecMax	:= SD1->(Recno())	//Guarda o registro com o maior valor
				
			EndIf
			
			SD1->(DbSkip())
			
		EndDo

		 //Monta string com pedidos distintos
        For nI := 1 To Len(aPedidos)
            cPedidos += aPedidos[nI] + IIf(nI < Len(aPedidos), "/", "")
        Next nI
		
	Endif 	
	
	//Posiciona no Registro
	If nRecMax > 0 
	
		SD1->(DbGoTo(nRecMax))	
		
		dbSelectArea('SC7')
		dbSetOrder(1)
		
		If dbSeek(xFilial('SC7')+SD1->D1_PEDIDO)
			cDescProd := Alltrim(SC7->C7_DESCRI)
		Else
			cDescProd := AllTrim(GetAdvFVal('SB1','B1_DESC', xFilial('SB1')+SD1->D1_COD,1))
		EndIf			
		
		cDescProd += "  / Pedido"+Iif(Len(aPedidos) > 1, "s", "")+": "+cPedidos
		
		If SD1->D1_RATEIO == "1"
			
			DbSelectArea("SDE")
			SDE->(DbSetOrder(1))
	
			SDE->(DbSeek(SD1->(D1_FILIAL + D1_DOC + D1_SERIE + D1_FORNECE + D1_LOJA + D1_ITEM)))
		
			//Grava os campos no arquivo de contas a pagar
			SE2->(DbGoTo(nRecE2))
			RecLock("SE2",.F.)
		
				SE2->E2_CCUSTO	:= SDE->DE_CC		//Centro de Custo
				SE2->E2_ITEMCTA	:= SDE->DE_ITEMCTA	//Item Contábil
				SE2->E2_CLVL	:= SDE->DE_CLVL		//Classe de Valor
				SE2->E2_HIST 	:= cDescProd		//Descrição do Produto do Primeiro Item
			
			SE2->(MsUnlock())
		
		Else	
			
			//Grava os campos no arquivo de contas a pagar
			SE2->(DbGoTo(nRecE2))
			RecLock("SE2",.F.)
		
				SE2->E2_CCUSTO	:= SD1->D1_CC		//Centro de Custo
				SE2->E2_ITEMCTA	:= SD1->D1_ITEMCTA	//Item Contábil
				SE2->E2_CLVL	:= SD1->D1_CLVL		//Classe de Valor
				SE2->E2_HIST 	:= cDescProd		//Descrição do Produto do Primeiro Item
			
			SE2->(MsUnlock())
		
		EndIf
	
	Endif 
	
	RestArea(aAreaSC7)
	RestArea(aAreaSD1)
	RestArea(aAreaSDE)
	RestArea(aArea)

Return Nil



//-------------------------------------------------------------------
/*/{Protheus.doc} GravaD1Imp
Grava os impostos de acordo com o csv importado      
@since 28/06/2024
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function GravaD1Imp()
Local aAreaSD1	:= SD1->(GetArea())
Local cChave    := SF1->F1_FILIAL + SF1->F1_DOC + SF1->F1_SERIE + SF1->F1_FORNECE + SF1->F1_LOJA
Local nItem     := 0
Local nBasePis  := 0
Local nBaseCof  := 0
Local nBaseCide := 0
Local nBaseIss  := 0
Local nBaseIR   := 0

Local nVlPis    := 0
Local nVlCof    := 0
Local nVlCide   := 0
Local nVlIss    := 0
Local nVlIR     := 0

Local nAlqCide  := 0
Local nAlqIss   := 0
Local nAlqIR    := 0
Local nAlqPis   := 0
Local nAlqCof   := 0

Local nTotBSCof := 0
Local nTotBsPIS := 0
Local nTotBsCID := 0

Local nTotIR    := 0
Local nTotCof   := 0
Local nTotPIS   := 0
Local nTotISS   := 0
Local nTotCID   := 0
	
SD1->(dbSetOrder(1))
If SD1->(dbSeek(cChave))
	While SD1->(!Eof()) .And. ( cChave == SD1->D1_FILIAL + SD1->D1_DOC + SD1->D1_SERIE + SD1->D1_FORNECE + SD1->D1_LOJA )

		nItem++
	   		
		// Base
		nBaseCIDE := RetImp("D1_BASECID", nItem)
		nBaseIss  := RetImp("D1_BASEISS", nItem)
		nBaseIR   := RetImp("D1_BASEIRR", nItem)
		nBasePis  := RetImp("D1_BASIMP6", nItem)
		nBaseCof  := RetImp("D1_BASIMP5", nItem)
		
		// Valor
		nVlCide   := RetImp("D1_VLCIDE" , nItem)
		nVlIss    := RetImp("D1_VALISS" , nItem)
		nVlIR     := RetImp("D1_VALIRR" , nItem)
		nVlPis    := RetImp("D1_VALIMP6", nItem)
		nVlCof    := RetImp("D1_VALIMP5", nItem)
		
		// Aliquota
		nAlqCide  := RetImp("D1_ALQCIDE", nItem)
		nAlqIss   := RetImp("D1_ALIQISS", nItem)
		nAlqIR    := RetImp("D1_ALIQIRR", nItem)
		nAlqPis   := RetImp("D1_ALQIMP6", nItem)
		nAlqCof   := RetImp("D1_ALQIMP5", nItem)

		nTotBSCof += nBaseCof  
		nTotBsPIS += nBasePis  
		nTotBsCID += nBaseCide  

		nTotIR  += nVlIR  
		nTotCof += nVlCof  
		nTotPIS += nVlPis  
		nTotISS += nVlIss
		nTotCID += nVlCide
		
		
		SD1->(RecLock("SD1",.F.))
		SD1->D1_BASECID  := nBaseCide
		SD1->D1_BASEISS  := nBaseIss 
		SD1->D1_BASEIRR  := nBaseIR  
		SD1->D1_BASIMP6  := nBasePis 
		SD1->D1_BASIMP5  := nBaseCof 
		
		SD1->D1_VLCIDE   := nVlCide
		SD1->D1_VALISS   := nVlIss 
		SD1->D1_VALIRR   := nVlIR  
		
		SD1->D1_VALIMP6  := nVlPis 
		SD1->D1_VALIMP5  := nVlCof 
		SD1->D1_ALQCIDE  := nAlqCide 
		SD1->D1_ALIQISS  := nAlqIss  
		SD1->D1_ALIQIRR  := nAlqIR   
		SD1->D1_ALQIMP6  := nAlqPis  
		SD1->D1_ALQIMP5  := nAlqCof  
		SD1->(MsUnlock())			
		
		SD1->(DbSkip())
	End
EndIf


RecLock("SF1",.F.)
SF1->F1_IRRF     := nTotIR
SF1->F1_BASIMP5  := nTotBSCof
SF1->F1_BASIMP6  := nTotBsPIS
SF1->F1_VALIMP5  := nTotCof
SF1->F1_VALIMP6  := nTotPIS
SF1->F1_ISS      := nTotISS
SF1->F1_VALIRF   := nTotIR
SF1->F1_VLCIDE   := nTotCID
SF1->F1_BASECID  := nTotBsCID
SF1->(MsUnlock())	

RestArea(aAreaSD1)

Return





//-------------------------------------------------------------------
/*/{Protheus.doc} RetImp
Busca os valores informados no csv para ser gravado
@since 28/06/2024
@version 1.0
/*/
//-------------------------------------------------------------------
Static Function RetImp(cImp, nItem)
Local nPos    := 0
Local nValor  := 0

nPos := aScan(aAutoItens[nItem], {|x| AllTrim(x[1]) = cImp	} )
If nPos > 0 
   nValor := aAutoItens[nItem,nPos,2]
EndIf 

Return nValor
