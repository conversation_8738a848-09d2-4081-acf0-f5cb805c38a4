#Include 'protheus.ch'

//-------------------------------------------------------------------
/*/{Protheus.doc} MT100GRV
P.E para para bloquear a exclusão de NCC no fechamento PMO Global.

<AUTHOR>
@since 08/06/2015
@version P12
/*/
//-------------------------------------------------------------------
User Function MT100GRV()
Local lRet			:= .T.
Local aArea			:= GetArea()
Local nPos			:= 0
Local aCFPParc		:= {}
Local aRetCFP		:= {}
Local nX			:= 0
Local nP			:= 0
Local nPosNF 		:= AScan(aHeader, {|x| AllTrim(x[2]) == "D1_NFORI"})
Local nPosSO 		:= AScan(aHeader, {|x| AllTrim(x[2]) == "D1_SERIORI"})
Local nPosNFIt 		:= AScan(aHeader, {|x| AllTrim(x[2]) == "D1_ITEM"})
Local nPosCD 		:= AScan(aHeader, {|x| AllTrim(x[2]) == "D1_COD"})
Local nPosIT 		:= AScan(aHeader, {|x| AllTrim(x[2]) == "D1_ITEMORI"})
Local lDelete		:= !INCLUI .And. !ALTERA
Local cIdPSA		:= IIf(SF1->(FieldPos("F1_XIDPSA")) > 0, SF1->F1_XIDPSA, "") // //ID do PSA para realizar integracao
Local lDelNota      := PARAMIXB[1]

DbSelectArea("SC7")
DbSetOrder(1)

nPos := AScan(aHeader, {|x| AllTrim(x[2]) == "D1_PEDIDO"})
nPosDc := AScan(aHeader, {|x| AllTrim(x[2]) == "D1_DOC"})

If SF1->F1_TIPO == "D" .And. lDelNota
	CancMensal()
EndIf

If nPos > 0
	If DbSeek(xFilial("SC7")+aCols[1][nPos])
                                        
		If Inclui

			If !Empty(AllTrim(SC7->C7_XSCFLU))	
 			
				lRet := U_EnvStPc(27,SC7->C7_XSCFLU,"Nota Fiscal Lanc.",aCols[1][nPosDc] )
			
			EndIf
	
		EndIf
	EndIf
	
	if lRet .and. len(aCols) > 0 .and. ( nPosNF > 0 .and. nPosSO > 0 .and. nPosCD > 0 .and. nPosIT > 0  )
	
		for nX := 1 to len(aCols)
			SD2->(DbSetOrder(3)) // D2_FILIAL+D2_DOC+D2_SERIE+D2_CLIENTE+D2_LOJA+D2_COD+D2_ITEM
			If SD2->(MsSeek( FWxFilial("SD2") + aCols[nX,nPosNF]+aCols[nX,nPosSO]+SF2->(F2_CLIENTE+F2_LOJA)+aCols[nX,nPosCD]+aCols[nX,nPosIT] )) .and. ;
				!Empty(SD2->D2_PEDIDO) .and. !Empty(SD2->D2_ITEMPV)
				SC6->(DbSetOrder(1)) //C6_FILIAL+C6_NUM+C6_ITEM+C6_PRODUTO
				If SC6->(MsSeek(FWxFilial("SC6")+SD2->(D2_PEDIDO+D2_ITEMPV))) .AND. !Empty(SC6->C6_XPRJCFP)
					SC5->(DbSetOrder(1))
					SC5->(MsSeek(FWxFilial("SC5")+SC6->C6_NUM))
		
					nP := AScan(aCFPParc,{|x| x[1]+x[2] == SC6->C6_XFILCFP+SC6->C6_XPRJCFP })
					If nP == 0
						AAdd(aCFPParc, { SC6->C6_XFILCFP, SC6->C6_XPRJCFP, {} })
						nP := Len(aCFPParc)
					EndIf
					AAdd(aCFPParc[nP,3], { SC6->C6_XPARCFP, SC6->C6_NUM, SC6->C6_ITEM, SC5->C5_EMISSAO, SD2->D2_DOC, SD2->D2_SERIE, SD2->D2_ITEM, SD2->D2_EMISSAO, SD1->D1_DOC, SD1->D1_SERIE, SD1->D1_QUANT, SD1->D1_VUNIT, SD1->D1_TOTAL, SF1->F1_MOEDA, SD1->D1_EMISSAO, SD1->D1_ITEM })
				EndIf
			EndIf
			//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
			//³ Atualiza parcelas do CFP                                                     ³
			//³ vai chamar a funcao  TSRV13PE para validacao, caso encontre algum problema   ³
			//³ nao atualiza, se tiver tudo ok, essa atualizacao vai acontecer no PE SD1100I ³
			//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
			if len(aCFPParc) > 0
				aRetCFP := U_TSRV13PE("4",aCFPParc,.F.,cIdPSA)
				if len(aRetCFP) > 0
					// qualquer item que tiver problema na validacao das parcelas CFP, aborta e nao deixa prosseguir	
					if !aRetCFP[1]
						lRet := aRetCFP[1]
						msgAlert('Projeto ' + aCFPParc[1,2] + ': ' + aRetCFP[2])
						exit
					endif
				endif
			endif	

			// Tratamento para Exclusão de NF Cancelada Parcial - Não permite excluir se saldo do item da NCC já foi faturado pelo contrato
			If lDelete .And. lRet .And. cTipo == "D" //Exclusão de Devolução
				lRet := VLDNCC(aCols[nX,nPosNF], aCols[nX,nPosSO], aCols[nX,nPosIT], aCols[nX,nPosNFIt], aCols[nX,nPosCD])
				If !lRet
					Exit
				EndIf
			EndIf

		next nX
		
	endif

	// Tratamento para Software Complementar
	If lDelete 
		U_COM03DNF()

		//Tratamento Json para API de integração com Mercado Eletronico
		If SuperGetMv("TI_INTME",,.F.)
			U_TCOMJ03D()
		EndIf

	EndIF
	
EndIf 

RestArea( aArea )
Return lRet

//ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
//±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
//±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
//±±ºPrograma  ³EnvStPc   ºAutor  ³Erich Buttner       º Data ³  24/04/14   º±±
//±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
//±±ºDesc.     ³ Envio de cancelamento para o Fluig                         º±±
//±±º          ³                                                            º±±
//±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
//±±ºUso       ³ P10                                                        º±±
//±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
//±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
//ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß


User Function EnvStPc (nState,cNumPc,cMsg ,cDoc)

	Local oSend		:= WSEcmWorkflowEngineServiceService():New()
	Local cPagePRC	:= GetMv("TI_#WSECM2")  // URL do StartProcessClassic
	Local nCompECM	:= GetMv("TI_#CREC01")  // ID da empresa no ECM
	Local cUserECM	:= GetMv("TI_#CREC02")  // usuario no ECM  // 777
	Local cPassECM	:= GetMv("TI_#CREC03") // senha no ecm
	Local lRet 		:= .F.
	Local aCardData := {}
	Local aCardAtta	:= {}
	Local aCardApo	:= {}

	Private cIdApr		:= ""
	Private lGO		:= .f.
	Private cIdGO		:= ""
	Private cIdGOApr	:= ""
	Private cIdSend	:= "16385127"

	cIdUsr := U_TGCTA01C()  //EmailFluig
	conout("testedd1",cIdUsr)
	cIdSend := U_ECMXGetId("",cIdUsr)
	conout("testedd1",cIdSend)
	
	oSend:_URL 				:= cPagePrc
	oSend:cUserName			:= cUserECM
	oSend:cPassword			:= cPassECM
	oSend:nCompanyId			:= nCompECM
	oSend:nprocessInstanceId	:= Val(cNumPc)
	oSend:nChoosedState		:= nState
	aAdd(oSend:oWSsaveAndSendTaskcolleagueIds:cItem,"System:Auto")	
	oSend:cComments			:= "Inclusão de Nota Fiscal"
	oSend:cUserID				:= Iif(Empty(AllTrim(cIdSend)),"16385127",cIdSend)
	oSend:lCompletetask		:= .t.

	aAdd(aCardAtta,EcmWorkflowEngineServiceService_processAttachmentDtoArray():New())		
	aAdd(aCardData,EcmWorkflowEngineServiceService_stringArrayArray():New())
	aAdd(aCardApo,EcmWorkflowEngineServiceService_processTaskAppointmentDtoArray():New())

	
	oSend:lManagerMode		:= .f.
	oSend:nthreadSequence 	:= 0
	
	oSend:saveAndSendTask()
	
	Conout(GetWSCError())

	If Len(oSend:oWSsaveAndSendTaskresult:OWSITEM) >= 3 //.Or. oSend:oWSsaveAndSendTaskresult:OWSITEM[1]:CITEM[1] <> "ERROR: " 
		
		DbSelectArea("PDU")
		DbSetOrder(5)
		If DbSeek(xFilial("PDU")+cNumPc)
			RecLock("PDU",.F.)
			PDU->PDU_STATUS := cMsg //oSend:oWSsaveAndSendTaskresult:OWSITEM[1]:CITEM[2]
			PDU->PDU_NFISCA := cDoc
			PDU->(MsUnLock())
		EndIf
			
		lRet := .T.

	Else
		
		//MsgStop(oSend:oWSsaveAndSendTaskresult:OWSITEM[1]:CITEM[2])
		
		DbSelectArea("PDU")
		DbSetOrder(5)
		If DbSeek(xFilial("PDU")+cNumPc)
			RecLock("PDU",.F.)
			PDU->PDU_STATUS := cMsg //oSend:oWSsaveAndSendTaskresult:OWSITEM[1]:CITEM[2]
			PDU->PDU_NFISCA := cDoc
			PDU->(MsUnLock())
		EndIf
		lRet := .T.

	EndIf


Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} M100VLDNCC
Tratamento para Exclusão de NF Cancelada Parcial
Não permite excluir se saldo do item da NCC já foi faturado pelo contrato

<AUTHOR> Ferreira
@since 27/07/2017
@return lRet
/*/
//-------------------------------------------------------------------
Static Function VLDNCC(cNFOri, cSerieOri, cItNFOri, cItemNF, cProduto)
Local aArea		:= GetArea()
Local lRet    	:= .T.
Local cTRBPH6   := ""
Local cGU		:= ""
Local cChave 	:= ""
Local cQuery    := ""
Local cUlRev    := ""
Default cNFOri		:= ""
Default cSerieOri	:= ""
Default cItNFOri	:= ""
Default cItemNF		:= ""
Default cProduto	:= ""

SD2->( dbsetorder(16) ) // D2_FILIAL+D2_DOC+D2_SERIE+D2_CLIENTE+D2_LOJA+D2_ITEM
If SD2->( dbseek(FWxFilial("SD2")+cNFOri+cSerieOri+cA100For+cLoja+cItNFOri) )
	SC6->( dbsetorder(1) ) // C6_FILIAL+C6_NUM+C6_ITEM+C6_PRODUTO
	If SC6->( dbseek(SD2->D2_FILIAL+SD2->D2_PEDIDO+SD2->D2_ITEMPV) )
		cUlRev  := GetUltRevis ()   
		cTRBPH6 := GetNextAlias()
		cGU		:= FwGrpCompany()+FwCodFil()
		cChave 	:= cGU+cNFOri+cSerieOri

		cQuery := " SELECT COUNT(1) CONTADOR "+CRLF
		cQuery += " FROM "+RetSqlName("PH6")+" PH6 "+CRLF

		cQuery += " INNER JOIN "+RetSqlName("PH5")+" PH5   " + CRLF
		cQuery += "   ON  PH5.PH5_FILIAL = '"+FWxFilial("PH5")+"' "+CRLF
		cQuery += "   AND PH5.PH5_CONTRA = PH6.PH6_CONTRA  " + CRLF
		cQuery += "   AND PH5.PH5_REVISA = PH6.PH6_REVISA  " + CRLF
		cQuery += "   AND PH5.PH5_NUMERO = PH6.PH6_NUMERO  " + CRLF
		cQuery += "   AND PH5.PH5_COMPET = PH6.PH6_COMPET  " + CRLF
		cQuery += "   AND PH5.PH5_CONDIC = PH6.PH6_CONDIC  " + CRLF
		cQuery += "   AND PH5.PH5_CLIENT = PH6.PH6_CLIENT  " + CRLF
		cQuery += "   AND PH5.PH5_LOJA   = PH6.PH6_LOJA    " + CRLF
		cQuery += "   AND PH5.PH5_CONDPG = PH6.PH6_CONDPG  " + CRLF
		cQuery += "   AND PH5.PH5_NOTASE = PH6.PH6_NOTASE  " + CRLF
		cQuery += "   AND PH5.PH5_MOEDA  = PH6.PH6_MOEDA   " + CRLF
		cQuery += "   AND PH5.PH5_MASCCC = PH6.PH6_MASCCC  " + CRLF
		cQuery += "   AND PH5.PH5_GU 	 = PH6.PH6_GU      " + CRLF
		cQuery += "   AND PH5.PH5_SEQ    = PH6.PH6_SEQ     " + CRLF
		cQuery += "   AND PH5.PH5_ITEM	 = '"+SC6->C6_XITMPLA+"' " + CRLF
		cQuery += "   AND PH5.D_E_L_E_T_ = ' ' " + CRLF

		cQuery += " WHERE PH6.PH6_FILIAL = '"+FWxFilial("PH6")+"' "+CRLF
		cQuery += "   AND PH6.PH6_GU	 IS NOT NULL  "+CRLF
		cQuery += "   AND PH6.PH6_NOTA	 IS NOT NULL  "+CRLF
		cQuery += "   AND PH6.PH6_SERIE  IS NOT NULL  "+CRLF
		cQuery += "   AND PH6.PH6_CHVPAI = '"+cChave+"' "+CRLF
		cQuery += "   AND PH6.D_E_L_E_T_ = ' ' "+CRLF
		cQuery += "   AND ( PH6.PH6_NOTA <> ' ' OR PH6.PH6_PEDVEN <> ' ' OR PH6.PH6_NUMMED <> ' ' ) "+CRLF
		cQuery += "   AND PH6.PH6_REVISA = '" + cUlRev + "' "+CRLF 
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cTRBPH6,.F.,.T.)

		(cTRBPH6)->( dbgotop() )
		If (cTRBPH6)->( !Eof() )
			If (cTRBPH6)->CONTADOR > 0
				Help(" ", 1, "Help", "MT100GRV_VLDNCC_01", "Exclusão de NCC não permitida, pois saldo do item já foi faturado pelo contrato do cliente. Necessário estonar cronograma faturado." + CRLF+;
										"Item: " + cItemNF + CRLF + ;
										"Produto: " + cProduto + CRLF +;
										"Descrição: " + Alltrim( Posicione("SB1", 1, FWxFilial("SB1") +	cProduto, "B1_DESC") ), 3, 0 )
				lRet := .F.
			EndIf
		EndIf
		(cTRBPH6)->( dbCloseArea() )

	EndIf                                               			
EndIf

RestArea(aArea)

Return lRet


/*{Protheus.doc} GetUltRevis
// Obter ultima revisao do Contrato
<AUTHOR> Leite
@since 29/04/2019
@version undefined
@type function
*/
Static function GetUltRevis ()
Local cCN9Revis := GetNextAlias() 		
Local cUlRev    := ''
 		
BeginSql Alias cCN9Revis
	SELECT MAX(CN9.CN9_REVISA) ULTREV 
	    FROM  %table:CN9% CN9 
       WHERE  CN9.CN9_NUMERO    = %Exp:(SC6->C6_CONTRT )% 
         AND  CN9.CN9_FILIAL    = %Exp:(FwxFilial('CN9'))% //CN9 é compartilhada 
         AND  CN9.%NotDel%     
EndSql
 		
If (cCN9Revis)->(!Eof())
	cUlRev := (cCN9Revis)->(ULTREV)
Endif

(cCN9Revis)->(DbCloseArea())

return cUlRev 


Static Function CancMensal()

	Local aAreaSD1 := SD1->(GetArea())
	Local aAreaSDE := SDE->(GetArea())
	Local aAreaP37 := P37->(GetArea())
	Local aCodP37  := {}
	Local cChvSD1  := SF1->(F1_FILIAL + F1_DOC + F1_SERIE + F1_FORNECE + F1_LOJA)
	

	P37->(DbSetOrder(1)) //P37_FILIAL+P37_COD
	SDE->(DbSetOrder(1)) // DE_FILIAL+DE_DOC+DE_SERIE+DE_FORNECE+DE_LOJA+DE_ITEMNF+DE_ITEM 

	SD1->(DbSetOrder(1) ) //D1_FILIAL+D1_DOC+D1_SERIE+D1_FORNECE+D1_LOJA+D1_COD+D1_ITEM
	SD1->(cChvSD1)

	While !SD1->(EOF()) .And. cChvSD1 == SD1->(D1_FILIAL + D1_DOC + D1_SERIE + D1_FORNECE + D1_LOJA)
		VerSDEPSA(aCodP37)
		SD1->(DbSkip()) 
	End

	CancP37PSA(aCodP37)
	

	RestArea(aAreaP37)
	RestArea(aAreaSDE)
	RestArea(aAreaSD1)
Return

Static Function VerSDEPSA(aCodP37)
	Local cChvSDE := FwxFilial("SDE") + SD1->D1_DOC + SD1->D1_SERIE + SD1->D1_FORNECE + SD1->D1_LOJA + SD1->D1_ITEM
	SDE->(DbSeek(cChvSDE)) 

	While SDE->(!EOF()) .and. cChvSDE == SDE->(DE_FILIAL + DE_DOC + DE_SERIE + DE_FORNECE + DE_LOJA + DE_ITEMNF)
		If !Empty(SDE->DE_XCODIN)
			If Ascan(aCodP37, SDE->DE_XCODIN) == 0
				AADD(aCodP37, SDE->DE_XCODIN)	
			EndIf
		EndIf
		SDE->(DbSkip())
	End

Return

Static Function CancP37PSA(aCodP37)
	Local ni       := 0
	Local cFilP37  := "00001000100"
	Local cJson    := ""
	Local aReplace := {}

	AADD(aReplace, {"@@IdNCC@@", ""})

	For ni := 1 to Len(aCodP37)
		If P37->(DbSeek(cFilP37 + aCodP37[ni]))
			If P37->P37_STATUS == "2" .and. "ctm_nccid" $ P37->P37_BODYRP
				aReplace[1, 2] := AllTrim(P37->P37_CODEXT)
				U_TCOMP37("000116", "", cJson, "", aReplace, "", '1')
			Else
				P37->(RecLock("P37", .F.))
					P37->(DbDelete())
				P37->(MsUnLock())
			EndIf
		EndIf
	Next

Return

